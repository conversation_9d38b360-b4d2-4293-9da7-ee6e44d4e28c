package cn.iocoder.yudao.module.urban.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {
    // ========== 任务管理 TODO 补充编号 ==========
    ErrorCode TASK_NOT_EXISTS = new ErrorCode(2_000_000_000, "任务管理不存在");
    // ========== 任务执行人 TODO 补充编号 ==========
    ErrorCode TASK_EXECUTOR_NOT_EXISTS = new ErrorCode(2_000_000_001, "任务执行人不存在");

    // ========== 行政区划 TODO 补充编号 ==========
    ErrorCode REGION_NOT_EXISTS = new ErrorCode(2_000_000_002, "行政区划不存在");
    ErrorCode REGION_EXITS_CHILDREN = new ErrorCode(2_000_000_003, "存在存在子行政区划，无法删除");
    ErrorCode REGION_PARENT_NOT_EXITS = new ErrorCode(2_000_000_004,"父级行政区划不存在");
    ErrorCode REGION_PARENT_ERROR = new ErrorCode(2_000_000_005, "不能设置自己为父行政区划");
    ErrorCode REGION_REGION_NAME_DUPLICATE = new ErrorCode(2_000_000_006, "已经存在该地区名称的行政区划");
    ErrorCode REGION_PARENT_IS_CHILD = new ErrorCode(2_000_000_007, "不能设置自己的子Region为父Region");
}
