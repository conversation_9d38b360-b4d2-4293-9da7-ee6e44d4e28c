package cn.iocoder.yudao.module.urban.dal.mysql.region;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.urban.controller.admin.region.vo.RegionListReqVO;
import cn.iocoder.yudao.module.urban.dal.dataobject.region.RegionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 行政区划 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RegionMapper extends BaseMapperX<RegionDO> {

    default List<RegionDO> selectList(RegionListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RegionDO>()
                .eqIfPresent(RegionDO::getParentId, reqVO.getParentId())
                .likeIfPresent(RegionDO::getName, reqVO.getName())
                .likeIfPresent(RegionDO::getFullName, reqVO.getFullName())
                .eqIfPresent(RegionDO::getStatus, reqVO.getStatus())
                .eqIfPresent(RegionDO::getRegionSeq, reqVO.getRegionSeq())
                .eqIfPresent(RegionDO::getCode, reqVO.getCode())
                .betweenIfPresent(RegionDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(RegionDO::getSortNo, reqVO.getSortNo())
                .eqIfPresent(RegionDO::getRegionLevel, reqVO.getRegionLevel())
                .likeIfPresent(RegionDO::getRankName, reqVO.getRankName())
                .orderByAsc(RegionDO::getCode));
    }

	default RegionDO selectByParentIdAndRegionName(String parentId, String name) {
	    return selectOne(RegionDO::getParentId, parentId, RegionDO::getName, name);
	}

    default Long selectCountByParentId(String parentId) {
        return selectCount(RegionDO::getParentId, parentId);
    }

}