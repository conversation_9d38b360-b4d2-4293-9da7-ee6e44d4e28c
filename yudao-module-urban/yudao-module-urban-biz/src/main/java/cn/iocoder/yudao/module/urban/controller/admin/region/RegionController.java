package cn.iocoder.yudao.module.urban.controller.admin.region;



import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.urban.controller.admin.region.vo.RegionListReqVO;
import cn.iocoder.yudao.module.urban.controller.admin.region.vo.RegionRespVO;
import cn.iocoder.yudao.module.urban.dal.dataobject.region.RegionDO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import cn.iocoder.yudao.module.urban.controller.admin.region.vo.RegionSaveReqVO;
import cn.iocoder.yudao.module.urban.service.region.RegionService;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 行政区划")
@RestController
@RequestMapping("/urban/region")
@Validated
public class RegionController {

    @Resource
    private RegionService regionService;

    @PostMapping("/create")
    @Operation(summary = "创建行政区划")
    @PreAuthorize("@ss.hasPermission('urban:region:create')")
    public CommonResult<String> createRegion(@Valid @RequestBody RegionSaveReqVO createReqVO) {
        return success(regionService.createRegion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新行政区划")
    @PreAuthorize("@ss.hasPermission('urban:region:update')")
    public CommonResult<Boolean> updateRegion(@Valid @RequestBody RegionSaveReqVO updateReqVO) {
        regionService.updateRegion(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除行政区划")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('urban:region:delete')")
    public CommonResult<Boolean> deleteRegion(@RequestParam("id") String id) {
        regionService.deleteRegion(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得行政区划")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('urban:region:query')")
    public CommonResult<RegionRespVO> getRegion(@RequestParam("id") String id) {
        RegionDO region = regionService.getRegion(id);
        return success(BeanUtils.toBean(region, RegionRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得行政区划列表")
    @PreAuthorize("@ss.hasPermission('urban:region:query')")
    public CommonResult<List<RegionRespVO>> getRegionList(@Valid RegionListReqVO listReqVO) {
        List<RegionDO> list = regionService.getRegionList(listReqVO);
        return success(BeanUtils.toBean(list, RegionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出行政区划 Excel")
    @PreAuthorize("@ss.hasPermission('urban:region:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRegionExcel(@Valid RegionListReqVO listReqVO,
              HttpServletResponse response) throws IOException {
        List<RegionDO> list = regionService.getRegionList(listReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "行政区划.xls", "数据", RegionRespVO.class,
                        BeanUtils.toBean(list, RegionRespVO.class));
    }

}