package cn.iocoder.yudao.module.urban.service.task;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.urban.controller.admin.task.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.task.TaskDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 任务管理 Service 接口
 *
 * <AUTHOR>
 */
public interface TaskService {

    /**
     * 创建任务管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTask(@Valid TaskSaveReqVO createReqVO);

    /**
     * 更新任务管理
     *
     * @param updateReqVO 更新信息
     */
    void updateTask(@Valid TaskSaveReqVO updateReqVO);

    /**
     * 删除任务管理
     *
     * @param id 编号
     */
    void deleteTask(Long id);

    /**
     * 获得任务管理
     *
     * @param id 编号
     * @return 任务管理
     */
    TaskDO getTask(Long id);

    /**
     * 获得任务管理分页
     *
     * @param pageReqVO 分页查询
     * @return 任务管理分页
     */
    PageResult<TaskDO> getTaskPage(TaskPageReqVO pageReqVO);

}