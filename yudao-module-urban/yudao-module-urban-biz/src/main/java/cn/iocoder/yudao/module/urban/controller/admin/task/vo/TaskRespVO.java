package cn.iocoder.yudao.module.urban.controller.admin.task.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 任务管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TaskRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "27365")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "任务编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21031")
    @ExcelProperty("任务编号")
    private String taskId;

    @Schema(description = "任务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "任务类型", converter = DictConvert.class)
    @DictFormat("uc_task_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String taskType;

    @Schema(description = "任务名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("任务名称")
    private String taskName;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("省")
    private String province;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("市")
    private String city;

    @Schema(description = "县区", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("县区")
    private String xzqdm;

    @Schema(description = "镇街")
    @ExcelProperty("镇街")
    private String town;

    @Schema(description = "社区村")
    @ExcelProperty("社区村")
    private String village;

    @Schema(description = "小区")
    @ExcelProperty("小区")
    private String community;

    @Schema(description = "组长ID", example = "27387")
    @ExcelProperty("组长ID")
    private String leaderId;

    @Schema(description = "组长", example = "李四")
    @ExcelProperty("组长")
    private String leaderName;

    @Schema(description = "状态：0-未完成|1-已完成", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @ExcelProperty(value = "状态：0-未完成|1-已完成", converter = DictConvert.class)
    @DictFormat("uc_task_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Short status;

    @Schema(description = "计划完成时间")
    @ExcelProperty("计划完成时间")
    private LocalDateTime plannedTime;

    @Schema(description = "实际完成时间")
    @ExcelProperty("实际完成时间")
    private LocalDateTime actualDate;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "图形", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("图形")
    private String geom;

    @Schema(description = "调查人集合", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("图形")
    private List<Long> surveyorIds;

}