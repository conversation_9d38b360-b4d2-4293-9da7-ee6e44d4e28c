package cn.iocoder.yudao.module.urban.service.taskexecutor;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.urban.controller.admin.taskexecutor.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.taskexecutor.TaskExecutorDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.urban.dal.mysql.taskexecutor.TaskExecutorMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.urban.enums.ErrorCodeConstants.*;

/**
 * 任务执行人 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TaskExecutorServiceImpl implements TaskExecutorService {

    @Resource
    private TaskExecutorMapper taskExecutorMapper;

    @Override
    public Long createTaskExecutor(TaskExecutorSaveReqVO createReqVO) {
        // 插入
        TaskExecutorDO taskExecutor = BeanUtils.toBean(createReqVO, TaskExecutorDO.class);
        taskExecutorMapper.insert(taskExecutor);
        // 返回
        return taskExecutor.getId();
    }

    @Override
    public void updateTaskExecutor(TaskExecutorSaveReqVO updateReqVO) {
        // 校验存在
        validateTaskExecutorExists(updateReqVO.getId());
        // 更新
        TaskExecutorDO updateObj = BeanUtils.toBean(updateReqVO, TaskExecutorDO.class);
        taskExecutorMapper.updateById(updateObj);
    }

    @Override
    public void deleteTaskExecutor(Long id) {
        // 校验存在
        validateTaskExecutorExists(id);
        // 删除
        taskExecutorMapper.deleteById(id);
    }

    @Override
    public void deleteTaskExecutorbyTaskId(Long taskId) {
        LambdaQueryWrapper<TaskExecutorDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskExecutorDO::getTaskId, taskId);
        taskExecutorMapper.delete(wrapper);
    }

    private void validateTaskExecutorExists(Long id) {
        if (taskExecutorMapper.selectById(id) == null) {
            throw exception(TASK_EXECUTOR_NOT_EXISTS);
        }
    }

    @Override
    public TaskExecutorDO getTaskExecutor(Long id) {
        return taskExecutorMapper.selectById(id);
    }

    @Override
    public PageResult<TaskExecutorDO> getTaskExecutorPage(TaskExecutorPageReqVO pageReqVO) {
        return taskExecutorMapper.selectPage(pageReqVO);
    }

}