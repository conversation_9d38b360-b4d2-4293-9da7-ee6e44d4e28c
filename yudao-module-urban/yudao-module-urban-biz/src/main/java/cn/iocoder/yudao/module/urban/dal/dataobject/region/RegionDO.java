package cn.iocoder.yudao.module.urban.dal.dataobject.region;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.urban.handler.PgGeometryTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 行政区划 DO
 *
 * <AUTHOR>
 */
@TableName(value = "uc_region",autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegionDO extends BaseDO {

    public static final String PARENT_ID_ROOT = "-99";

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 父级id
     */
    private String parentId;
    /**
     * 地区名称
     */
    private String name;
    /**
     * 地区全名
     */
    private String fullName;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 序列
     */
    private String regionSeq;
    /**
     * 地区编号
     */
    private String code;
    /**
     * 排列顺序-从0开始, 数值越小越前
     */
    private Integer sortNo;
    /**
     * 层级
     */
    private Integer regionLevel;
    /**
     * 层级名称
     */
    private String rankName;

    /**
     * 图形
     */
    @TableField(typeHandler = PgGeometryTypeHandler.class)
    private String geom;

}