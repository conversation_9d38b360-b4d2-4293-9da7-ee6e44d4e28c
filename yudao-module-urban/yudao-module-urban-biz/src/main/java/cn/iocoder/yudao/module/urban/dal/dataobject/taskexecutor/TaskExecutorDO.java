package cn.iocoder.yudao.module.urban.dal.dataobject.taskexecutor;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 任务执行人 DO
 *
 * <AUTHOR>
 */
@TableName("uc_task_executor")
@KeySequence("uc_task_executor_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskExecutorDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户类型：调查员|其他
     *
     * 枚举 {@link TODO uc_task_executor_user_type 对应的类}
     */
    private String userType;

}