package cn.iocoder.yudao.module.urban.controller.admin.taskexecutor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 任务执行人新增/修改 Request VO")
@Data
public class TaskExecutorSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17460")
    private Long id;

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32325")
    private Long taskId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6173")
    private Long userId;

    @Schema(description = "用户类型：调查员|其他", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "用户类型：调查员|其他不能为空")
    private String userType;

}