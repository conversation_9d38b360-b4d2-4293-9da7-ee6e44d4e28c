package cn.iocoder.yudao.module.urban.service.region;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.urban.controller.admin.region.vo.RegionListReqVO;
import cn.iocoder.yudao.module.urban.controller.admin.region.vo.RegionSaveReqVO;
import cn.iocoder.yudao.module.urban.dal.dataobject.region.RegionDO;
import cn.iocoder.yudao.module.urban.dal.mysql.region.RegionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.urban.enums.ErrorCodeConstants.*;

/**
 * 行政区划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RegionServiceImpl implements RegionService {

    @Resource
    private RegionMapper regionMapper;

    @Override
    public String createRegion(RegionSaveReqVO createReqVO) {
        // 校验父级id的有效性
        validateParentRegion(null, createReqVO.getParentId());
        // 校验地区名称的唯一性
        validateRegionRegionNameUnique(null, createReqVO.getParentId(), createReqVO.getName());

        // 插入
        RegionDO region = BeanUtils.toBean(createReqVO, RegionDO.class);


        regionMapper.insert(region);
        // 返回
        return region.getId();
    }

    @Override
    public void updateRegion(RegionSaveReqVO updateReqVO) {
        // 校验存在
        validateRegionExists(updateReqVO.getId());
        // 校验父级id的有效性
        validateParentRegion(updateReqVO.getId(), updateReqVO.getParentId());
        // 校验地区名称的唯一性
        validateRegionRegionNameUnique(updateReqVO.getId(), updateReqVO.getParentId(), updateReqVO.getName());

        // 更新
        RegionDO updateObj = BeanUtils.toBean(updateReqVO, RegionDO.class);
        regionMapper.updateById(updateObj);
    }

    @Override
    public void deleteRegion(String id) {
        // 校验存在
        validateRegionExists(id);
        // 校验是否有子行政区划
        if (regionMapper.selectCountByParentId(id) > 0) {
            throw exception(REGION_EXITS_CHILDREN);
        }
        // 删除
        regionMapper.deleteById(id);
    }

    private void validateRegionExists(String id) {
        if (regionMapper.selectById(id) == null) {
            throw exception(REGION_NOT_EXISTS);
        }
    }

    private void validateParentRegion(String id, String parentId) {
        if (parentId == null || RegionDO.PARENT_ID_ROOT.equals(parentId)) {
            return;
        }
        // 1. 不能设置自己为父行政区划
        if (Objects.equals(id, parentId)) {
            throw exception(REGION_PARENT_ERROR);
        }
        // 2. 父行政区划不存在
        RegionDO parentRegion = regionMapper.selectById(parentId);
        if (parentRegion == null) {
            throw exception(REGION_PARENT_NOT_EXITS);
        }
        // 3. 递归校验父行政区划，如果父行政区划是自己的子行政区划，则报错，避免形成环路
        if (id == null) { // id 为空，说明新增，不需要考虑环路
            return;
        }
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            // 3.1 校验环路
            parentId = parentRegion.getParentId();
            if (Objects.equals(id, parentId)) {
                throw exception(REGION_PARENT_IS_CHILD);
            }
            // 3.2 继续递归下一级父行政区划
            if (parentId == null || RegionDO.PARENT_ID_ROOT.equals(parentId)) {
                break;
            }
            parentRegion = regionMapper.selectById(parentId);
            if (parentRegion == null) {
                break;
            }
        }
    }

    private void validateRegionRegionNameUnique(String id, String parentId, String regionName) {
        RegionDO region = regionMapper.selectByParentIdAndRegionName(parentId, regionName);
        if (region == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的行政区划
        if (id == null) {
            throw exception(REGION_REGION_NAME_DUPLICATE);
        }
        if (!Objects.equals(region.getId(), id)) {
            throw exception(REGION_REGION_NAME_DUPLICATE);
        }
    }

    @Override
    public RegionDO getRegion(String id) {
        return regionMapper.selectById(id);
    }

    @Override
    public List<RegionDO> getRegionList(RegionListReqVO listReqVO) {
        // 处理 parentCode 查询逻辑：根据父级编码查询出父级id，再作为parentId条件查询子级
        if (StrUtil.isNotBlank(listReqVO.getParentCode())) {
            // 根据 parentCode 查询父级区划记录
            RegionDO parentRegion = regionMapper.selectOne(RegionDO::getCode, listReqVO.getParentCode());
            if (parentRegion == null) {
                // 如果根据 parentCode 查询不到对应记录，返回空列表
                return Collections.emptyList();
            }
            // 将查询到的父级id设置为查询条件，parentCode优先级高于parentId
            listReqVO.setParentId(parentRegion.getId());
        }

        return regionMapper.selectList(listReqVO);
    }

}