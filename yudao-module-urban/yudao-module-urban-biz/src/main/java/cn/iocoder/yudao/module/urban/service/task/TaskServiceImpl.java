package cn.iocoder.yudao.module.urban.service.task;

import cn.iocoder.yudao.module.urban.controller.admin.taskexecutor.vo.TaskExecutorSaveReqVO;
import cn.iocoder.yudao.module.urban.dal.dataobject.taskexecutor.TaskExecutorDO;
import cn.iocoder.yudao.module.urban.dal.mysql.taskexecutor.TaskExecutorMapper;
import cn.iocoder.yudao.module.urban.service.taskexecutor.TaskExecutorService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.urban.controller.admin.task.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.task.TaskDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.urban.dal.mysql.task.TaskMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.urban.enums.ErrorCodeConstants.*;

/**
 * 任务管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TaskServiceImpl implements TaskService {

    @Resource
    private TaskMapper taskMapper;
    @Resource
    private TaskExecutorService taskExecutorService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTask(TaskSaveReqVO createReqVO) {
        // 插入
        TaskDO task = BeanUtils.toBean(createReqVO, TaskDO.class);
        taskMapper.insert(task);

        List<Long> surveyorIds = createReqVO.getSurveyorIds();
        if(!surveyorIds.isEmpty()){
            // 添加新数据
            surveyorIds.forEach(surveyorId -> {
                TaskExecutorSaveReqVO taskExecutorSaveReqVO = new TaskExecutorSaveReqVO();
                taskExecutorSaveReqVO.setTaskId(task.getId());
                taskExecutorSaveReqVO.setUserId(surveyorId);
                taskExecutorSaveReqVO.setUserType("DCY");
                taskExecutorService.createTaskExecutor(taskExecutorSaveReqVO);
            });
        }

        //todo 需要对不同类型的  业务数据进行绑定taskid
        // 返回
        return task.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTask(TaskSaveReqVO updateReqVO) {
        // 校验存在
        validateTaskExists(updateReqVO.getId());
        // 更新
        TaskDO updateObj = BeanUtils.toBean(updateReqVO, TaskDO.class);
        taskMapper.updateById(updateObj);

        List<Long> surveyorIds = updateReqVO.getSurveyorIds();
        if(!surveyorIds.isEmpty()){
            // 删除原有数据
            taskExecutorService.deleteTaskExecutorbyTaskId(updateReqVO.getId());
            // 添加新数据
            surveyorIds.forEach(surveyorId -> {
                TaskExecutorSaveReqVO taskExecutorSaveReqVO = new TaskExecutorSaveReqVO();
                taskExecutorSaveReqVO.setTaskId(updateReqVO.getId());
                taskExecutorSaveReqVO.setUserId(surveyorId);
                taskExecutorSaveReqVO.setUserType("DCY");
                taskExecutorService.createTaskExecutor(taskExecutorSaveReqVO);
            });
        }

        //todo 需要更新对不同类型的  业务数据进行绑定taskid
    }

    @Override
    public void deleteTask(Long id) {
        // 校验存在
        validateTaskExists(id);
        // 删除
        taskMapper.deleteById(id);
        //删除相关 调查人员记录
        taskExecutorService.deleteTaskExecutorbyTaskId(id);
        //todo 需要删除对应业务数据的 taskid
    }

    private void validateTaskExists(Long id) {
        if (taskMapper.selectById(id) == null) {
            throw exception(TASK_NOT_EXISTS);
        }
    }

    @Override
    public TaskDO getTask(Long id) {

        TaskDO task = taskMapper.selectById(id);
        if (task == null) {
            return null;
        }
        // 2. 使用关联查询获取执行人ID列表
        List<Long> surveyorIds = taskMapper.selectJoinList(
                Long.class,
                new MPJLambdaWrapper<TaskDO>()
                        .select(TaskExecutorDO::getUserId)
                        .leftJoin(TaskExecutorDO.class, TaskExecutorDO::getTaskId, TaskDO::getId)
                        .eq(TaskDO::getId, id)
                        .isNotNull(TaskExecutorDO::getUserId)
        );

        // 3. 设置执行人ID列表
        task.setSurveyorIds(surveyorIds != null ? surveyorIds : new ArrayList<>());

        return task;

    }

    @Override
    public PageResult<TaskDO> getTaskPage(TaskPageReqVO pageReqVO) {

        PageResult<TaskDO>  taskPage = taskMapper.selectPage(pageReqVO);

        if(taskPage.getList().isEmpty()){
           return taskPage;
        }

        List<Long> taskIds = taskPage.getList().stream()
                .map(TaskDO::getId)
                .collect(Collectors.toList());

        List<TaskExecutorDO> executors = taskMapper.selectJoinList(
                TaskExecutorDO.class,
                new MPJLambdaWrapper<TaskDO>()
                        .selectAll(TaskExecutorDO.class)
                        .leftJoin(TaskExecutorDO.class, TaskExecutorDO::getTaskId, TaskDO::getId)
                        .in(TaskDO::getId, taskIds)
        );

        // 3. 按任务ID分组
        Map<Long, List<Long>> taskExecutorMap = executors.stream()
                .collect(Collectors.groupingBy(
                        TaskExecutorDO::getTaskId,
                        Collectors.mapping(TaskExecutorDO::getUserId, Collectors.toList())
                ));

        // 4. 设置每个任务的执行人ID列表
        taskPage.getList().forEach(task -> {
            List<Long> surveyorIds = taskExecutorMap.getOrDefault(task.getId(), new ArrayList<>());
            task.setSurveyorIds(surveyorIds);
        });


        return taskPage;
    }

}