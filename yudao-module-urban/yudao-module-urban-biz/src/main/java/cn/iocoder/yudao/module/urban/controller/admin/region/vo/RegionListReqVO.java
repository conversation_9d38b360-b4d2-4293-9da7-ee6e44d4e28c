package cn.iocoder.yudao.module.urban.controller.admin.region.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 行政区划列表 Request VO")
@Data
public class RegionListReqVO {

    @Schema(description = "父级id", example = "19009")
    private String parentId;

    @Schema(description = "父级Code", example = "220100")
    private String parentCode;

    @Schema(description = "地区名称", example = "芋艿")
    private String name;

    @Schema(description = "地区全名", example = "李四")
    private String fullName;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "序列")
    private String regionSeq;

    @Schema(description = "地区编号")
    private String code;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "排列顺序-从0开始, 数值越小越前")
    private Integer sortNo;

    @Schema(description = "层级")
    private Integer regionLevel;

    @Schema(description = "层级名称", example = "芋艿")
    private String rankName;

    @Schema(description = "图形")
    private String geom;

}