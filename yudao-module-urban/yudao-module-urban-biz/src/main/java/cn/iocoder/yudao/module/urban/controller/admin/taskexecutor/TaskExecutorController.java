package cn.iocoder.yudao.module.urban.controller.admin.taskexecutor;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.urban.controller.admin.taskexecutor.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.taskexecutor.TaskExecutorDO;
import cn.iocoder.yudao.module.urban.service.taskexecutor.TaskExecutorService;

@Tag(name = "管理后台 - 任务执行人")
@RestController
@RequestMapping("/urban/task-executor")
@Validated
public class TaskExecutorController {

    @Resource
    private TaskExecutorService taskExecutorService;

    @PostMapping("/create")
    @Operation(summary = "创建任务执行人")
    @PreAuthorize("@ss.hasPermission('urban:task-executor:create')")
    public CommonResult<Long> createTaskExecutor(@Valid @RequestBody TaskExecutorSaveReqVO createReqVO) {
        return success(taskExecutorService.createTaskExecutor(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新任务执行人")
    @PreAuthorize("@ss.hasPermission('urban:task-executor:update')")
    public CommonResult<Boolean> updateTaskExecutor(@Valid @RequestBody TaskExecutorSaveReqVO updateReqVO) {
        taskExecutorService.updateTaskExecutor(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除任务执行人")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('urban:task-executor:delete')")
    public CommonResult<Boolean> deleteTaskExecutor(@RequestParam("id") Long id) {
        taskExecutorService.deleteTaskExecutor(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得任务执行人")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('urban:task-executor:query')")
    public CommonResult<TaskExecutorRespVO> getTaskExecutor(@RequestParam("id") Long id) {
        TaskExecutorDO taskExecutor = taskExecutorService.getTaskExecutor(id);
        return success(BeanUtils.toBean(taskExecutor, TaskExecutorRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得任务执行人分页")
    @PreAuthorize("@ss.hasPermission('urban:task-executor:query')")
    public CommonResult<PageResult<TaskExecutorRespVO>> getTaskExecutorPage(@Valid TaskExecutorPageReqVO pageReqVO) {
        PageResult<TaskExecutorDO> pageResult = taskExecutorService.getTaskExecutorPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TaskExecutorRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出任务执行人 Excel")
    @PreAuthorize("@ss.hasPermission('urban:task-executor:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTaskExecutorExcel(@Valid TaskExecutorPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TaskExecutorDO> list = taskExecutorService.getTaskExecutorPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "任务执行人.xls", "数据", TaskExecutorRespVO.class,
                        BeanUtils.toBean(list, TaskExecutorRespVO.class));
    }

}