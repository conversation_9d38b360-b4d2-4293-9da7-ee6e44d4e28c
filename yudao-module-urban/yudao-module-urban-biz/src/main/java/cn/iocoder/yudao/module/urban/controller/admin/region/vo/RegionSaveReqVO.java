package cn.iocoder.yudao.module.urban.controller.admin.region.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 行政区划新增/修改 Request VO")
@Data
public class RegionSaveReqVO {

    @Schema(description = "主键",  example = "26757")
    private String id;

    @Schema(description = "父级id",requiredMode = Schema.RequiredMode.REQUIRED, example = "19009")
    @NotEmpty(message = "上级区划不能为空")
    private String parentId;

    @Schema(description = "地区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "地区名称不能为空")
    private String name;

    @Schema(description = "地区全名", example = "李四")
    private String fullName;

    @Schema(description = "状态",  example = "2")
    private Integer status;

    @Schema(description = "序列")
    private String regionSeq;

    @Schema(description = "区划代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "区划代码不能为空")
    private String code;

    @Schema(description = "排列顺序-从0开始, 数值越小越前")
    private Integer sortNo;

    @Schema(description = "层级")
    private Integer regionLevel;

    @Schema(description = "层级名称", example = "芋艿")
    private String rankName;

    @Schema(description = "图形", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "图形不能为空")
    private String geom;

}