package cn.iocoder.yudao.module.urban.service.taskexecutor;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.urban.controller.admin.taskexecutor.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.taskexecutor.TaskExecutorDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 任务执行人 Service 接口
 *
 * <AUTHOR>
 */
public interface TaskExecutorService {

    /**
     * 创建任务执行人
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTaskExecutor(@Valid TaskExecutorSaveReqVO createReqVO);

    /**
     * 更新任务执行人
     *
     * @param updateReqVO 更新信息
     */
    void updateTaskExecutor(@Valid TaskExecutorSaveReqVO updateReqVO);

    /**
     * 删除任务执行人
     *
     * @param id 编号
     */
    void deleteTaskExecutor(Long id);

    void deleteTaskExecutorbyTaskId(Long taskId);

    /**
     * 获得任务执行人
     *
     * @param id 编号
     * @return 任务执行人
     */
    TaskExecutorDO getTaskExecutor(Long id);

    /**
     * 获得任务执行人分页
     *
     * @param pageReqVO 分页查询
     * @return 任务执行人分页
     */
    PageResult<TaskExecutorDO> getTaskExecutorPage(TaskExecutorPageReqVO pageReqVO);

}