package cn.iocoder.yudao.module.urban.controller.admin.task.vo;

import jakarta.validation.constraints.NotNull;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 任务管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TaskPageReqVO extends PageParam {

    @Schema(description = "任务编号", example = "21031")
    private String taskId;

    @Schema(description = "任务类型", example = "1")
    private String taskType;

    @Schema(description = "任务名称", example = "张三")
    private String taskName;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "县区")
    private String xzqdm;

    @Schema(description = "镇街")
    private String town;

    @Schema(description = "社区村")
    private String village;

    @Schema(description = "小区")
    private String community;

    @Schema(description = "组长", example = "李四")
    private String leaderName;

    @Schema(description = "状态：0-未完成|1-已完成", example = "0")
    private Integer status;

    @Schema(description = "计划完成时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] plannedTime;

    @Schema(description = "实际完成时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] actualDate;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "图形")
    private String geom;

    @Schema(description = "调查人集合")
    private List<Long> surveyorIds;

}