package cn.iocoder.yudao.module.urban;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 项目的启动类
 *
 * 如果你碰到启动的问题，请认真阅读 https://cloud.iocoder.cn/quick-start/ 文章
 * 如果你碰到启动的问题，请认真阅读 https://cloud.iocoder.cn/quick-start/ 文章
 * 如果你碰到启动的问题，请认真阅读 https://cloud.iocoder.cn/quick-start/ 文章
 *
 * <AUTHOR>
 */
@SpringBootApplication
public class UrbanServerApplication {

    public static void main(String[] args) {

        SpringApplication.run(UrbanServerApplication.class, args);

    }

}
