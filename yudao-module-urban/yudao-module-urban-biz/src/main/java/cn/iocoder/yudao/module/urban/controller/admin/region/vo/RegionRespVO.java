package cn.iocoder.yudao.module.urban.controller.admin.region.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 行政区划 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RegionRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "26757")
    @ExcelProperty("主键")
    private String id;

    @Schema(description = "父级id", example = "19009")
    @ExcelProperty("父级id")
    private String parentId;

    @Schema(description = "地区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("地区名称")
    private String name;

    @Schema(description = "地区全名", example = "李四")
    @ExcelProperty("地区全名")
    private String fullName;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "序列", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("序列")
    private String regionSeq;

    @Schema(description = "地区编号")
    @ExcelProperty("地区编号")
    private String code;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "排列顺序-从0开始, 数值越小越前", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排列顺序-从0开始, 数值越小越前")
    private Integer sortNo;

    @Schema(description = "层级")
    @ExcelProperty("层级")
    private Integer regionLevel;

    @Schema(description = "层级名称", example = "芋艿")
    @ExcelProperty("层级名称")
    private String rankName;

    @Schema(description = "图形", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("图形")
    private String geom;

}