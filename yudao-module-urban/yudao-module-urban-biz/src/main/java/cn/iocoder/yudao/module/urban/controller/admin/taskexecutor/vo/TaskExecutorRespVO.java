package cn.iocoder.yudao.module.urban.controller.admin.taskexecutor.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 任务执行人 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TaskExecutorRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17460")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32325")
    @ExcelProperty("任务ID")
    private Long taskId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6173")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "用户类型：调查员|其他", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "用户类型：调查员|其他", converter = DictConvert.class)
    @DictFormat("uc_task_executor_user_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String userType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}