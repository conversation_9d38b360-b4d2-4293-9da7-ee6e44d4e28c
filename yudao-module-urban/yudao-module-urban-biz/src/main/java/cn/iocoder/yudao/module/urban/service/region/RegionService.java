package cn.iocoder.yudao.module.urban.service.region;

import cn.iocoder.yudao.module.urban.controller.admin.region.vo.RegionListReqVO;
import cn.iocoder.yudao.module.urban.controller.admin.region.vo.RegionSaveReqVO;
import cn.iocoder.yudao.module.urban.dal.dataobject.region.RegionDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 行政区划 Service 接口
 *
 * <AUTHOR>
 */
public interface RegionService {

    /**
     * 创建行政区划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createRegion(@Valid RegionSaveReqVO createReqVO);

    /**
     * 更新行政区划
     *
     * @param updateReqVO 更新信息
     */
    void updateRegion(@Valid RegionSaveReqVO updateReqVO);

    /**
     * 删除行政区划
     *
     * @param id 编号
     */
    void deleteRegion(String id);

    /**
     * 获得行政区划
     *
     * @param id 编号
     * @return 行政区划
     */
    RegionDO getRegion(String id);

    /**
     * 获得行政区划列表
     *
     * @param listReqVO 查询条件
     * @return 行政区划列表
     */
    List<RegionDO> getRegionList(RegionListReqVO listReqVO);

}